const { ipcRenderer } = require('electron');
const fs = require('fs');
const path = require('path');
const { clipboard } = require('electron');

// 左侧栏元素
const folderHistory = document.getElementById('folder-history');
const openFolderBtn = document.getElementById('open-folder');
const folderTree = document.getElementById('folder-tree');
const tagTree = document.getElementById('tag-tree');

// 全局变量提前声明
let tagsData = {}; // 存储标签定义 (tag ID → tag name)
let fileTags = {}; // 存储文件标签关联 (relative file path → [tag IDs])
let fileWebInfo = {}; // 存储文件的网址和备注信息

// Tag管理系统 - 提前声明全局变量

// 存储打开过的文件夹历史
let folderHistoryList = JSON.parse(localStorage.getItem('folderHistory')) || [];
console.log('Folder history loaded:', folderHistoryList);
updateHistoryDropdown();
// 默认加载第一个历史记录
if (folderHistoryList.length > 0) {
  renderFolderTree(folderHistoryList[0].path);
}

// 保存文件夹历史到localStorage
function saveFolderHistory() {
  localStorage.setItem('folderHistory', JSON.stringify(folderHistoryList));
  console.log('Folder history saved:', folderHistoryList);
}

// 打开文件夹按钮点击事件
openFolderBtn.addEventListener('click', async () => {
  const result = await ipcRenderer.invoke('open-folder-dialog');
  if (!result.canceled && result.filePaths.length > 0) {
    const selectedFolder = result.filePaths[0];
    addToHistory(selectedFolder);
    renderFolderTree(selectedFolder);
  }
});

// 添加到历史记录
function addToHistory(folderPath) {
  const folderName = path.basename(folderPath);
  folderHistoryList.push({
    path: folderPath,
    name: folderName
  });
  saveFolderHistory();
  updateHistoryDropdown();
}

// 更新历史下拉框
function updateHistoryDropdown() {
  folderHistory.innerHTML = '';
  folderHistoryList.forEach((item, index) => {
    const option = document.createElement('option');
    option.value = index;
    option.textContent = `${index + 1}. ${item.name}`;
    folderHistory.appendChild(option);
  });

  // 添加历史选择事件
  folderHistory.addEventListener('change', (e) => {
    const selectedIndex = e.target.value;
    if (selectedIndex >= 0 && selectedIndex < folderHistoryList.length) {
      const selectedFolder = folderHistoryList[selectedIndex].path;
      renderFolderTree(selectedFolder);
    }
  });

  // 默认选中第一个选项
  if (folderHistoryList.length > 0) {
    folderHistory.value = 0;
  }
}

// 渲染文件夹树
function renderFolderTree(rootPath) {
  // 设置根目录路径
  window.rootFolderPath = rootPath;

  // 通过IPC将根路径传递给主进程，供API服务器使用
  ipcRenderer.invoke('set-root-folder-path', rootPath);

  document.querySelector('.folder-item')?.classList.remove('active');

  // 加载文件标签和网址信息
  loadFileTags();
  loadFileWebInfo();

  // 更新状态显示
  document.getElementById('main-status').textContent = '文件夹状态';

  // 加载标签数据
  loadFileTags();

  // 清空现有树
  folderTree.innerHTML = '';

  // 递归构建文件夹树
  function buildTree(currentPath, parentElement) {
    const files = fs.readdirSync(currentPath);

    files.forEach(file => {
      if (file.startsWith('.')) return;

      const fullPath = path.join(currentPath, file);
      const stats = fs.statSync(fullPath);

      if (stats.isDirectory()) {
        const folderItem = document.createElement('div');
        folderItem.className = 'folder-item';

        // 检查当前文件夹的直接子文件夹
        const children = fs.readdirSync(fullPath);
        const subfolders = children.filter(child => {
          const childPath = path.join(fullPath, child);
          return !child.startsWith('.') && fs.statSync(childPath).isDirectory();
        });

        // 只有有子文件夹时才添加图标
        if (subfolders.length > 0) {
          const icon = document.createElement('span');
          icon.className = 'folder-icon';
          icon.textContent = '►';
          folderItem.appendChild(icon);
        }

        // 添加文件夹名称
        const nameSpan = document.createElement('span');
        nameSpan.textContent = file;
        folderItem.appendChild(nameSpan);

        folderItem.dataset.path = fullPath;

        const childContainer = document.createElement('div');
        childContainer.className = 'folder-children';
        childContainer.style.display = 'none';

        folderItem.addEventListener('click', (e) => {
          const wasActive = folderItem.classList.contains('active');

          if (e.detail === 1) { // 单击
            loadFolderContents(fullPath);
            筛选状态 = '文件夹';
            document.getElementById('remove-from-tag-btn').style.display = 'none';
            //document.getElementById('batch-toolbar').style.display = 'none'
            // 选中当前文件夹
            document.querySelectorAll('.folder-item').forEach(item => {
              item.classList.remove('active');
            });
            document.querySelectorAll('.tag-item').forEach(item => {
              item.classList.remove('active');
            });
            folderItem.classList.add('active');
            // 更新状态显示
            document.getElementById('main-status').textContent = fullPath;
          }
          e.stopPropagation();

          // 如果文件夹之前不是激活状态，总是展开子文件夹
          if (!wasActive) {
            childContainer.style.display = 'block';
            const icon = folderItem.querySelector('.folder-icon');
            if (icon) {
              icon.textContent = '▼';
            }
          } else {
            // 如果文件夹已经是激活状态，则切换展开/折叠状态
            const isHidden = childContainer.style.display === 'none';
            childContainer.style.display = isHidden ? 'block' : 'none';
            const icon = folderItem.querySelector('.folder-icon');
            if (icon) {
              icon.textContent = isHidden ? '▼' : '►';
            }
          }
        });
        // 修改 renderFolderTree 函数中的文件夹点击事件处理部分
        folderItem.addEventListener('contextmenu', (e) => {
          e.preventDefault();
          const folderPath = folderItem.dataset.path;

          // 移除现有菜单
          const oldMenu = document.querySelector('.folder-context-menu');
          if (oldMenu) oldMenu.remove();

          // 创建菜单
          const menu = document.createElement('div');
          menu.className = 'folder-context-menu';
          menu.style.position = 'fixed';
          menu.style.left = `${e.clientX}px`;
          menu.style.top = `${e.clientY}px`;
          menu.style.background = 'white';
          menu.style.border = '1px solid #ddd';
          menu.style.borderRadius = '4px';
          menu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
          menu.style.zIndex = '1000';
          menu.style.minWidth = '120px';

          // 添加菜单项
          const createFolderOption = document.createElement('div');
          createFolderOption.textContent = '新建文件夹';
          createFolderOption.style.padding = '8px 16px';
          createFolderOption.style.cursor = 'pointer';
          createFolderOption.onclick = () => createNewFolder(folderPath);

          const renameOption = document.createElement('div');
          renameOption.textContent = '重命名';
          renameOption.style.padding = '8px 16px';
          renameOption.style.cursor = 'pointer';
          renameOption.onclick = () => renameFolder(folderPath);

          const openInExplorerOption = document.createElement('div');
          openInExplorerOption.textContent = '在资源管理器里打开';
          openInExplorerOption.style.padding = '8px 16px';
          openInExplorerOption.style.cursor = 'pointer';
          openInExplorerOption.onclick = () => openFolderInExplorer(folderPath);

          menu.appendChild(createFolderOption);
          menu.appendChild(renameOption);
          menu.appendChild(openInExplorerOption);
          document.body.appendChild(menu);

          // 点击其他地方关闭菜单
          const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
              menu.remove();
              document.removeEventListener('click', closeMenu);
            }
          };
          setTimeout(() => document.addEventListener('click', closeMenu), 100);
        });
        parentElement.appendChild(folderItem);
        parentElement.appendChild(childContainer);

        buildTree(fullPath, childContainer);
      }
    });
  }

  // 添加主文件夹节点
  const rootItem = document.createElement('div');
  rootItem.className = 'folder-item';

  // 添加展开/折叠图标
  const rootIcon = document.createElement('span');
  rootIcon.className = 'folder-icon';
  rootIcon.textContent = '►';
  rootItem.appendChild(rootIcon);

  // 添加文件夹名称
  const rootName = document.createElement('span');
  rootName.textContent = path.basename(rootPath);
  rootItem.appendChild(rootName);

  rootItem.dataset.path = rootPath;

  const rootChildren = document.createElement('div');
  rootChildren.className = 'folder-children';

  rootItem.addEventListener('click', (e) => {
    const wasActive = rootItem.classList.contains('active');

    if (e.detail === 1) { // 单击
      loadFolderContents(rootPath); // 加载文件夹内容
      // 设置根文件夹为激活状态
      document.querySelectorAll('.folder-item').forEach(item => {
        item.classList.remove('active');
      });
      document.querySelectorAll('.tag-item').forEach(item => {
        item.classList.remove('active');
      });
      rootItem.classList.add('active');
    }
    e.stopPropagation();

    // 如果文件夹之前不是激活状态，总是展开子文件夹
    if (!wasActive) {
      rootChildren.style.display = 'block';
      const icon = rootItem.querySelector('.folder-icon');
      icon.textContent = '▼';
    } else {
      // 如果文件夹已经是激活状态，则切换展开/折叠状态
      const isHidden = rootChildren.style.display === 'none';
      rootChildren.style.display = isHidden ? 'block' : 'none';
      const icon = rootItem.querySelector('.folder-icon');
      icon.textContent = isHidden ? '▼' : '►';
    }
  });


  function updateInfoJson(oldFolderPath, newFolderPath) {
    // 规范化路径格式
    oldFolderPath = oldFolderPath.replace(/\\/g, '/');
    newFolderPath = newFolderPath.replace(/\\/g, '/');
    oldFolderPath = oldFolderPath.split('/')[oldFolderPath.split('/').length - 1] + '/';
    newFolderPath = newFolderPath.split('/')[newFolderPath.split('/').length - 1] + '/';

    // 确保路径以斜杠结尾
    if (!oldFolderPath.endsWith('/')) oldFolderPath += '/';
    if (!newFolderPath.endsWith('/')) newFolderPath += '/';

    const infoPath = path.join(window.rootFolderPath, 'info.json');
    console.log('Updating info.json at:', infoPath, oldFolderPath, newFolderPath);

    try {
      if (fs.existsSync(infoPath)) {
        const info = JSON.parse(fs.readFileSync(infoPath, 'utf-8'));

        // 用于存储需要更新的条目
        const updatedEntries = {};

        // 遍历所有条目，查找包含旧文件夹路径的条目
        Object.keys(info).forEach(relativePath => {
          if (relativePath.startsWith(oldFolderPath)) {
            // 计算新路径
            const newRelativePath = newFolderPath + relativePath.slice(oldFolderPath.length);

            // 复制标签数据
            updatedEntries[newRelativePath] = info[relativePath];
            console.log(`Updating "${relativePath}" to "${newRelativePath}"`);
          }
        });

        // 如果没有找到任何匹配的条目，记录错误并返回
        if (Object.keys(updatedEntries).length === 0) {
          console.error(`No entries found in info.json that start with "${oldFolderPath}"`);
          return;
        }

        // 更新info对象
        Object.assign(info, updatedEntries);

        // 删除旧路径条目
        Object.keys(info).forEach(relativePath => {
          if (relativePath.startsWith(oldFolderPath)) {
            delete info[relativePath];
          }
        });

        // 写回文件
        fs.writeFileSync(infoPath, JSON.stringify(info, null, 2));
        console.log('info.json updated successfully');
        //
      } else {
        console.error('info.json does not exist');
      }
    } catch (error) {
      console.error('更新 info.json 失败:', error);
    }
  }

  // 新建文件夹
  function createNewFolder(parentFolderPath) {
    (async () => {
      const folderName = await prompt("请输入新文件夹名称:", "新建文件夹");
      if (folderName && folderName.trim()) {
        const newFolderPath = path.join(parentFolderPath, folderName.trim());
        try {
          // 检查文件夹是否已存在
          if (fs.existsSync(newFolderPath)) {
            提示('文件夹已存在');
            return;
          }

          // 创建文件夹
          fs.mkdirSync(newFolderPath, { recursive: true });
          renderFolderTree(window.rootFolderPath); // 重新渲染文件夹树
          提示('文件夹创建成功');
        } catch (error) {
          console.error('创建文件夹失败:', error);
          提示('创建文件夹失败: ' + error.message);
        }
      }
    })();
  }

  function renameFolder(folderPath) {
    (async () => {
      const newName = await prompt("请输入新文件夹名称:", path.basename(folderPath));
      if (newName) {
        const newPath = path.join(path.dirname(folderPath), newName);
        try {
          fs.renameSync(folderPath, newPath);
          renderFolderTree(window.rootFolderPath); // 重新渲染文件夹树
          updateInfoJson(folderPath, newPath); // 更新 info.json
        } catch (error) {
          console.error('重命名文件夹失败:', error);
        }
      }
    })();
  }

  function prompt(text, defaultValue = '') {
    return new Promise((resolve) => {
      document.getElementById('表格').style.display = 'block';
      document.getElementById('prompt-text').value = defaultValue;
      document.getElementById('prompt-old').textContent = "原名: " + defaultValue;

      let 保存 = document.getElementById('提交');
      保存.onclick = function () {
        document.getElementById('表格').style.display = 'none';
        const newname = document.getElementById('prompt-text').value;
        resolve(newname);
      };

      let 取消 = document.getElementById('取消');
      取消.onclick = function () {
        document.getElementById('表格').style.display = 'none';
        resolve(null); // 用户取消时返回null
      };

      // 添加输入框以便用户输入新名称
      /*const input = document.createElement('input');
      input.type = 'text';
      input.id = 'prompt-input';
      input.value = defaultValue;
      document.getElementById('表格').insertBefore(input, 保存);*/
    });
  }


  // 添加在资源管理器里打开文件夹的函数
  function openFolderInExplorer(folderPath) {
    const { shell } = require('electron');
    shell.showItemInFolder(folderPath);
  }
  folderTree.appendChild(rootItem);
  folderTree.appendChild(rootChildren);
  buildTree(rootPath, rootChildren);
}

// 全局排序状态
let currentSortBy = 'date'; // 'name', 'date', 'size'
let currentSortOrder = 'desc'; // 'asc', 'desc'

// 加载文件夹内容
function loadFolderContents(folderPath) {
  //console.log('加载文件夹内容:', folderPath);
  // 清空现有内容
  const mainContent = document.getElementById('main-content');
  mainContent.innerHTML = '';

  // 添加工具栏
  const toolbar = createFileListToolbar(folderPath);
  mainContent.appendChild(toolbar);

  // 创建文件容器
  const fileContainer = document.createElement('div');
  fileContainer.id = 'file-container';
  fileContainer.style.display = 'flex';
  fileContainer.style.flexWrap = 'wrap';
  fileContainer.style.gap = '10px';
  fileContainer.style.padding = '10px 0';
  mainContent.appendChild(fileContainer);

  // 读取并排序文件
  const files = fs.readdirSync(folderPath);
  const fileList = [];

  files.forEach(file => {
    if (file.startsWith('.')) return;

    const fullPath = path.join(folderPath, file);
    const stats = fs.statSync(fullPath);

    if (!stats.isDirectory()) {
      fileList.push({
        name: file,
        path: fullPath,
        stats: stats,
        size: stats.size,
        mtime: stats.mtime
      });
    }
  });

  // 排序文件列表
  const sortedFiles = sortFileList(fileList);

  // 渲染文件
  sortedFiles.forEach(fileInfo => {
    renderThumbnail2(fileInfo.path, fileContainer);// 渲染缩略图
  });

  handleFolderDrop();// 处理拖拽事件
}

// 创建文件列表工具栏
function createFileListToolbar(folderPath) {
  const toolbar = document.createElement('div');
  toolbar.className = 'file-list-toolbar';
  toolbar.style.cssText = `
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 10px;
    border: 1px solid #dee2e6;
  `;

  // 排序标签
  const sortLabel = document.createElement('span');
  sortLabel.textContent = '排序方式:';
  sortLabel.style.fontWeight = 'bold';
  sortLabel.style.color = '#495057';

  // 排序选择器
  const sortSelect = document.createElement('select');
  sortSelect.style.cssText = `
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    cursor: pointer;
  `;

  const sortOptions = [
    { value: 'name', text: '文件名' },
    { value: 'date', text: '修改日期' },
    { value: 'size', text: '文件大小' }
  ];

  sortOptions.forEach(option => {
    const optionElement = document.createElement('option');
    optionElement.value = option.value;
    optionElement.textContent = option.text;
    optionElement.selected = option.value === currentSortBy;
    sortSelect.appendChild(optionElement);
  });

  // 排序顺序按钮
  const orderButton = document.createElement('button');
  orderButton.style.cssText = `
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
  `;
  updateOrderButton(orderButton);

  // 事件监听
  sortSelect.addEventListener('change', () => {
    currentSortBy = sortSelect.value;
    loadFolderContents(folderPath);
  });

  orderButton.addEventListener('click', () => {
    currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
    loadFolderContents(folderPath);
  });

  toolbar.appendChild(sortLabel);
  toolbar.appendChild(sortSelect);
  toolbar.appendChild(orderButton);

  return toolbar;
}

// 更新排序顺序按钮
function updateOrderButton(button) {
  const isAsc = currentSortOrder === 'asc';
  button.innerHTML = `
    <span>${isAsc ? '↑' : '↓'}</span>
    <span>${isAsc ? '升序' : '降序'}</span>
  `;
}

// 排序文件列表
function sortFileList(fileList) {
  return fileList.sort((a, b) => {
    let comparison = 0;

    switch (currentSortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name, 'zh-CN', { numeric: true });
        break;
      case 'date':
        comparison = a.mtime.getTime() - b.mtime.getTime();
        break;
      case 'size':
        comparison = a.size - b.size;
        break;
    }

    return currentSortOrder === 'asc' ? comparison : -comparison;
  });
}

// 支持的图片扩展名
const IMAGE_EXTS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
const THUMB_DIR = 'thum';

// 确保缩略图目录存在
function ensureThumbDir() {
  if (!fs.existsSync(THUMB_DIR)) {
    fs.mkdirSync(THUMB_DIR);
  }
}

// 获取缩略图路径
function getThumbPath(filePath) {
  const hash = require('crypto').createHash('md5').update(filePath).digest('hex');
  return path.join(THUMB_DIR, `${hash}.jpg`);
}

// 生成缩略图
async function generateThumbnail(filePath, thumbPath) {
  return new Promise((resolve) => {
    const ext = path.extname(filePath).toLowerCase();
    if (IMAGE_EXTS.includes(ext)) {
      const { nativeImage } = require('electron');
      const image = nativeImage.createFromPath(filePath);
      const resized = image.resize({ width: 150 });
      fs.writeFileSync(thumbPath, resized.toJPEG(80));
    } else {
      resolve();
    }
  });
}

// 全局变量存储选中的文件
let selectedFiles = new Set();
let lastSelectedIndex = -1;
let thumbnailElements = [];

function removeFromCurrentTag() {
  if (!currentFilterTag || selectedFiles.size === 0) return;

  // 确认操作
  if (!confirm(`确定要从标签"${currentFilterTag}"移除 ${selectedFiles.size} 个文件吗？`)) return;

  // 找到对应标签ID
  const tagId = Object.keys(tagsData).find(key => tagsData[key] === currentFilterTag);
  if (!tagId) return;

  // 从所有选中文件中移除该标签
  selectedFiles.forEach(filePath => {
    if (fileTags[filePath]) {
      fileTags[filePath] = fileTags[filePath].filter(id => id !== tagId);
      // 从DOM中移除
      document.querySelector(`.thumbnail-container[data-path="${filePath}"]`)?.remove();
    }
  });

  // 保存并更新状态
  saveFileTags();
  selectedFiles.clear();
  updateBatchToolbar();
}

// 标签系统配置
const TAGS_FILE = 'tags.json';

function loadTags() {
  try {
    if (fs.existsSync(TAGS_FILE)) {
      tagsData = JSON.parse(fs.readFileSync(TAGS_FILE, 'utf-8'));

      renderTagTree(); // 加载标签后立即渲染标签树
    }
  } catch (e) {
    console.error('Error loading tags:', e);
  }
}

// 渲染标签树
function renderTagTree() {
  tagTree.innerHTML = '';

  // 按标签分类分组
  const tagCategories = {};
  Object.values(tagsData).forEach(tagName => {
    const category = tagName.split(':')[0] || '未分类';
    if (!tagCategories[category]) {
      tagCategories[category] = [];
    }
    tagCategories[category].push(tagName);
  });

  // 渲染分类和标签
  Object.entries(tagCategories).forEach(([category, tags]) => {
    const categoryItem = document.createElement('div');
    categoryItem.className = 'tag-category';

    const categoryName = document.createElement('div');
    categoryName.className = 'category-name';

    const icon = document.createElement('span');
    icon.className = 'category-icon';
    icon.textContent = '►';
    icon.style.marginRight = '5px';
    categoryName.appendChild(icon);

    const nameSpan = document.createElement('span');
    nameSpan.textContent = category;
    categoryName.appendChild(nameSpan);

    const tagList = document.createElement('div');
    tagList.className = 'tag-list';
    tagList.style.display = 'none';

    tags.forEach(tagName => {
      const tagItem = document.createElement('div');
      tagItem.className = 'tag-item';
      //获取标签id
      const tagId = Object.keys(tagsData).find(key => tagsData[key] === tagName);
      // 添加缩进
      const indent = document.createElement('span');
      //indent.style.display = 'inline-block'
      //indent.style.width = '15px'
      tagItem.appendChild(indent);

      const tagText = document.createElement('span');
      tagText.textContent = tagName.split(':')[1] || tagName;
      tagItem.appendChild(tagText);

      // 添加点击筛选功能
      tagItem.addEventListener('click', () => {
        filterByTag(tagName);
        document.querySelectorAll('.folder-item').forEach(item => {
          item.classList.remove('active');
        });
        document.querySelectorAll('.tag-item').forEach(item => {
          item.classList.remove('active');
        });
        tagItem.classList.toggle('active');
      });

      tagList.appendChild(tagItem);

      // 添加右键菜单事件
      tagItem.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        const tagName = tagsData[tagId];

        // 移除现有菜单
        const oldMenu = document.querySelector('.tag-context-menu');
        if (oldMenu) oldMenu.remove();

        // 创建菜单
        const menu = document.createElement('div');
        menu.className = 'tag-context-menu';
        menu.style.position = 'fixed';
        menu.style.left = `${e.clientX}px`;
        menu.style.top = `${e.clientY}px`;
        menu.style.background = 'white';
        menu.style.border = '1px solid #ddd';
        menu.style.borderRadius = '4px';
        menu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '120px';

        // 添加菜单项
        const renameOption = document.createElement('div');
        renameOption.textContent = '重命名';
        renameOption.style.padding = '8px 16px';
        renameOption.style.cursor = 'pointer';
        renameOption.onclick = () => renameTag(tagId);

        const deleteOption = document.createElement('div');
        deleteOption.textContent = '删除';
        deleteOption.style.padding = '8px 16px';
        deleteOption.style.cursor = 'pointer';
        deleteOption.style.color = 'red';
        deleteOption.onclick = () => deleteTag(tagId);

        menu.appendChild(renameOption);
        menu.appendChild(deleteOption);
        document.body.appendChild(menu);

        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
          if (!menu.contains(e.target)) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
          }
        };
        setTimeout(() => document.addEventListener('click', closeMenu), 100);
      });
    });

    // 添加分类展开/折叠功能
    categoryName.addEventListener('click', () => {
      const isHidden = tagList.style.display === 'none';
      tagList.style.display = isHidden ? 'flex' : 'none';
      icon.textContent = isHidden ? '▼' : '►';
      console.log(tagList.style.display, icon.textContent);
    });

    categoryItem.appendChild(categoryName);
    categoryItem.appendChild(tagList);
    tagTree.appendChild(categoryItem);
  });
}

// 按标签筛选文件
let currentFilterTag = null
let 筛选状态 = null
function filterByTag(tagName) {
  const mainContent = document.getElementById('main-content')
  mainContent.innerHTML = ''
  筛选状态 = '标签'
  // 更新状态显示和按钮
  currentFilterTag = tagName
  document.getElementById('main-status').textContent = `筛选标签: ${tagName}`
  document.getElementById('remove-from-tag-btn').style.display = 'none'
  //document.getElementById('batch-toolbar').style.display = 'none'

  // 找到所有包含该标签的文件
  const matchingFiles = Object.entries(fileTags)
    .filter(([filePath, tagIds]) =>
      tagIds.some(tagId => tagsData[tagId] === tagName)
    )
    .map(([filePath]) => filePath)

  // 渲染匹配的文件
  matchingFiles.forEach(filePath => {
    renderThumbnail2(filePath)
  })
}

// 初始化Tag系统
loadTags();

function loadFileTags() {
  if (!window.rootFolderPath) {
    console.log('rootFolderPath is not set, skipping loadFileTags');
    return;
  }

  try {
    const infoFile = path.join(window.rootFolderPath, 'info.json');

    if (fs.existsSync(infoFile)) {
      const rawData = fs.readFileSync(infoFile, 'utf-8');
      const relativeFileTags = JSON.parse(rawData);

      // 将相对路径转换为绝对路径
      // 使用新变量名避免覆盖全局变量
      const loadedFileTags = {};
      Object.keys(relativeFileTags).forEach(relativePath => {
        const absolutePath = path.join(window.rootFolderPath, relativePath);
        loadedFileTags[absolutePath] = relativeFileTags[relativePath];
      });

      // 将加载的数据赋值给全局变量
      fileTags = loadedFileTags;
    } else {
      fileTags = {};
    }
  } catch (e) {
    console.error('Error loading file tags:', e);
    fileTags = {};
  }
}

function saveTags() {
  fs.writeFileSync(TAGS_FILE, JSON.stringify(tagsData, null, 2))
}

function saveFileTags() {
  if (!window.rootFolderPath) return;

  const infoFile = path.join(window.rootFolderPath, 'info.json');
  const relativeFileTags = {};

  // 将绝对路径转换为相对路径
  Object.keys(fileTags).forEach(absolutePath => {
    const relativePath = path.relative(window.rootFolderPath, absolutePath);
    relativeFileTags[relativePath] = fileTags[absolutePath];
  });

  fs.writeFileSync(infoFile, JSON.stringify(relativeFileTags, null, 2));
}

// 加载文件网址和备注信息
function loadFileWebInfo() {
  if (!window.rootFolderPath) {
    console.log('rootFolderPath is not set, skipping loadFileWebInfo');
    return;
  }

  try {
    const webInfoFile = path.join(window.rootFolderPath, 'webinfo.json');

    if (fs.existsSync(webInfoFile)) {
      const rawData = fs.readFileSync(webInfoFile, 'utf-8');
      const relativeData = JSON.parse(rawData);

      // 将相对路径转换为绝对路径
      const loadedData = {};
      Object.keys(relativeData).forEach(relativePath => {
        const absolutePath = path.join(window.rootFolderPath, relativePath);
        loadedData[absolutePath] = relativeData[relativePath];
      });

      // 将加载的数据赋值给全局变量
      fileWebInfo = loadedData;
    } else {
      fileWebInfo = {};
    }
  } catch (e) {
    console.error('Error loading file web info:', e);
    fileWebInfo = {};
  }
}

// 保存文件网址和备注信息
function saveFileWebInfo() {
  if (!window.rootFolderPath) return;

  const webInfoFile = path.join(window.rootFolderPath, 'webinfo.json');
  const relativeData = {};

  // 将绝对路径转换为相对路径
  Object.keys(fileWebInfo).forEach(absolutePath => {
    const relativePath = path.relative(window.rootFolderPath, absolutePath);
    relativeData[relativePath] = fileWebInfo[absolutePath];
  });

  fs.writeFileSync(webInfoFile, JSON.stringify(relativeData, null, 2));
}

function generateTagId() {
  return Math.floor(10000 + Math.random() * 90000).toString()
}



// 创建文件信息面板和批量工具栏



// 添加移除按钮
const removeBtn = document.getElementById('remove-from-tag-btn')
removeBtn.addEventListener('click', removeFromCurrentTag)

// 悬停效果
removeBtn.addEventListener('mouseover', () => {
  removeBtn.style.background = '#e60000'
})
removeBtn.addEventListener('mouseout', () => {
  removeBtn.style.background = '#ff4d4d'
})

//document.getElementById('状态栏').appendChild(removeBtn)


// 批量工具栏
const batchToolbar = document.createElement('div')
batchToolbar.id = 'batch-toolbar'
batchToolbar.style.display = 'none'

// 更新批量工具栏
function updateBatchToolbar() {

  if (selectedFiles.size > 0) {
    //batchToolbar.style.display = 'block'
    //如果当前为标签筛选状态，显示移除按钮
    if (筛选状态 == '标签') {
      removeBtn.style.display = 'block'
    } else {
      removeBtn.style.display = 'none'
    }

  } else {
    //batchToolbar.style.display = 'none'
    removeBtn.style.display = 'none'
  }
}


// 删除folderHistoryManager相关代码

// 当前选中的文件
let selectedFile = null

// 渲染文件信息面板
// 修改 renderer.js 中的 renderFileInfoPanel 函数，添加双击事件处理
function renderFileInfoPanel() {
  const infoPanel = document.getElementById('right-sidebar')
  if (!infoPanel) return

  infoPanel.innerHTML = ''

  if (!selectedFile) return

  const stats = fs.statSync(selectedFile)

  // 文件基本信息
  const fileInfo = document.createElement('div')
  fileInfo.className = 'file-info'
  fileInfo.id = 'file-info'

  const fileName = document.createElement('h3')
  fileName.style.overflow = 'overlay'
  fileName.textContent = path.basename(selectedFile)

  const fileSize = document.createElement('p')
  fileSize.textContent = `大小: ${(stats.size / 1024).toFixed(2)} KB`

  const fileModified = document.createElement('p')
  fileModified.textContent = `修改时间: ${new Date(stats.mtime).toLocaleString()}`

  fileInfo.appendChild(fileName)
  fileInfo.appendChild(fileSize)
  fileInfo.appendChild(fileModified)
  infoPanel.appendChild(fileInfo)

  // 显示缩略图
  const ext = path.extname(selectedFile).toLowerCase()
  const thumbnailContainer = document.createElement('div')
  thumbnailContainer.className = 'thumbnail-container'

  const thumbnail = document.createElement('img')
  thumbnail.className = 'thumbnail'

  // 确保缩略图目录存在
  ensureThumbDir()

  // 获取缩略图路径
  const thumbPath = getThumbPath(selectedFile)
  if (IMAGE_EXTS.includes(ext) || ext === '.psd') {
    if (fs.existsSync(thumbPath)) {
      thumbnail.src = thumbPath
      thumbnail.alt = path.basename(selectedFile)
    } else {
      thumbnail.style.fontSize = '120px'
      thumbnail.alt = '🖼️'
    }
  } else if (ext === '.mp4' || ext === '.mov' || ext === '.avi' || ext === '.mkv') {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '📹'
  } else if (ext === '.ai' || ext === '.psd' || ext === '.psb' || ext === '.eps' || ext === '.cdr') {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '🎨'
  } else if (ext === '.html') {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '🌐'
  } else {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '📄'
  }

  thumbnailContainer.appendChild(thumbnail)
  infoPanel.appendChild(thumbnailContainer)

  // 添加“粘贴替换缩略图”按钮
  const pasteButton = document.createElement('button')
  pasteButton.textContent = '粘贴替换缩略图'
  pasteButton.classList.add('t2')
  pasteButton.addEventListener('click', handlePasteThumbnail)
  infoPanel.appendChild(pasteButton)

  // Tag管理区域
  const tagSection = document.createElement('div')
  tagSection.className = 'tag-section'

  const tagTitle = document.createElement('h4')
  tagTitle.textContent = '标签'

  const tagList = document.createElement('div')
  tagList.className = 'tag-list'

  // 修改 renderFileInfoPanel 函数中的 tag 显示部分
  if (fileTags[selectedFile]) {
    fileTags[selectedFile].forEach(tagId => {
      const tag = document.createElement('span');
      tag.className = 'tag';
      if (tagsData[tagId]) {
        tag.textContent = tagsData[tagId].split(':')[1] || tagId;

        // 添加移除按钮
        const removeBtn = document.createElement('span');
        removeBtn.textContent = ' ❌';
        removeBtn.style.cursor = 'pointer';
        removeBtn.style.color = 'red';
        removeBtn.addEventListener('click', () => {
          removeTagFromFile(selectedFile, tagId);
        });

        tag.appendChild(removeBtn);
        tagList.appendChild(tag);
      }
    });
  }

  // 添加移除标签的函数
  function removeTagFromFile(filePath, tagId) {
    if (!fileTags[filePath]) return;

    fileTags[filePath] = fileTags[filePath].filter(id => id !== tagId);
    saveFileTags();
    renderFileInfoPanel();
  }

  // 添加tag按钮
  const addTagBtn = document.createElement('button')
  addTagBtn.textContent = '+ 添加标签'
  addTagBtn.addEventListener('click', () => showTagSelector())
  addTagBtn.classList.add('t2')
  tagSection.appendChild(tagTitle)
  tagSection.appendChild(tagList)
  tagSection.appendChild(addTagBtn)
  infoPanel.appendChild(tagSection)

  // 备注和网址编辑区域
  const webInfoSection = document.createElement('div')
  webInfoSection.className = 'web-info-section'
  webInfoSection.style.cssText = `

  `;

  // 备注编辑
  const noteTitle = document.createElement('h4')
  noteTitle.textContent = '备注'
  noteTitle.style.marginBottom = '8px'

  const noteTextarea = document.createElement('textarea')
  noteTextarea.placeholder = '添加备注信息...'
  noteTextarea.style.cssText = `
    width: calc(100% - 20px);
    height: 60px;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    font-size: 12px;
    margin-bottom: 15px;
  `;

  // 获取当前文件的备注信息
  const currentWebInfo = fileWebInfo[selectedFile] || {};
  noteTextarea.value = currentWebInfo.note || '';

  // 网址编辑
  const urlTitle = document.createElement('h4')
  urlTitle.textContent = '网址'
  urlTitle.style.marginBottom = '8px'

  const urlContainer = document.createElement('div')
  urlContainer.style.cssText = `
    display: flex;
    gap: 8px;
    align-items: center;
  `;

  const urlInput = document.createElement('input')
  urlInput.type = 'url'
  urlInput.placeholder = '输入图片来源网址...'
  urlInput.style.cssText = `
    flex: 1;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 12px;
  `;
  urlInput.value = currentWebInfo.url || '';

  const visitButton = document.createElement('button')
  visitButton.textContent = '访问'
  visitButton.style.cssText = `
    padding: 8px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
  `;
  visitButton.disabled = !urlInput.value;

  // 访问按钮事件
  visitButton.addEventListener('click', () => {
    if (urlInput.value) {
      require('electron').shell.openExternal(urlInput.value);
    }
  });

  // 输入框变化时更新按钮状态
  urlInput.addEventListener('input', () => {
    visitButton.disabled = !urlInput.value;
  });

  // 保存备注和网址的函数
  const saveWebInfo = () => {
    if (!fileWebInfo[selectedFile]) {
      fileWebInfo[selectedFile] = {};
    }
    fileWebInfo[selectedFile].note = noteTextarea.value;
    fileWebInfo[selectedFile].url = urlInput.value;
    saveFileWebInfo();
  };

  // 添加自动保存
  noteTextarea.addEventListener('blur', saveWebInfo);
  urlInput.addEventListener('blur', saveWebInfo);

  urlContainer.appendChild(urlInput)
  urlContainer.appendChild(visitButton)

  webInfoSection.appendChild(noteTitle)
  webInfoSection.appendChild(noteTextarea)
  webInfoSection.appendChild(urlTitle)
  webInfoSection.appendChild(urlContainer)
  infoPanel.appendChild(webInfoSection)

  // 软件列表区域
  const softwareSection = document.createElement('div')
  softwareSection.className = 'software-section'

  const softwareTitle = document.createElement('h4')
  softwareTitle.textContent = '可用软件'

  const softwareList = document.createElement('div')
  softwareList.className = 'software-list'

  // 显示可用软件
  const softwareListData = JSON.parse(localStorage.getItem('softwareList')) || [];
  softwareListData.forEach(software => {
    const softwareItem = document.createElement('div')
    softwareItem.className = 'software-item'

    const softwareName = document.createElement('span')
    softwareName.textContent = software.name

    const openBtn = document.createElement('button')
    openBtn.className = 'open-btn'
    openBtn.textContent = software.name
    openBtn.addEventListener('click', () => openFileWithSoftware(software.path, selectedFile))

    //softwareItem.appendChild(softwareName)
    softwareItem.appendChild(openBtn)
    softwareList.appendChild(softwareItem)
  })

  softwareSection.appendChild(softwareTitle)
  softwareSection.appendChild(softwareList)
  infoPanel.appendChild(softwareSection)

  // 添加双击事件处理
  thumbnailContainer.addEventListener('dblclick', () => {
    const ext = path.extname(selectedFile).toLowerCase();
    const softwareListData = JSON.parse(localStorage.getItem('softwareList')) || [];
    const matchingSoftware = softwareListData.find(software => {
      const formats = software.guanlian ? software.guanlian.split(' ') : [];
      return formats.includes(ext);
    });

    if (matchingSoftware) {
      openFileWithSoftware(matchingSoftware.path, selectedFile);
    } else {
      //alert('没有找到关联的软件来打开此文件');
    }
  });
}

// 使用指定软件打开文件
function openFileWithSoftware(softwarePath, filePath) {
  const { platform } = require('os')
  const { exec } = require('child_process')

  // 打印调试信息
  console.log(`软件路径: ${softwarePath}`)
  console.log(`文件路径: ${filePath}`)

  let command
  if (platform() === 'win32') {
    command = `"${softwarePath}" "${filePath}"`
  } else {
    // 确保路径中没有多余的引号
    softwarePath = softwarePath.replace(/^"(.*)"$/, '$1')
    filePath = filePath.replace(/^"(.*)"$/, '$1')
    command = `open -a "${softwarePath}" "${filePath}"`
  }

  console.log(`执行命令: ${command}`)

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`执行命令失败: ${error.message}`)
      return
    }
    if (stderr) {
      console.error(`stderr: ${stderr}`)
      return
    }
    console.log(`stdout: ${stdout}`)
  })
}
// 显示tag选择器
function showTagSelector() {
  const selector = document.createElement('div')
  selector.className = 'tag-selector'



  // 添加关闭按钮
  const closeBtn = document.createElement('button')
  closeBtn.textContent = '×'
  closeBtn.style.position = 'absolute'
  closeBtn.style.top = '10px'
  closeBtn.style.right = '10px'
  closeBtn.style.fontSize = '20px'
  closeBtn.style.background = 'none'
  closeBtn.style.border = 'none'
  closeBtn.style.cursor = 'pointer'
  closeBtn.addEventListener('click', () => {
    document.body.removeChild(selector)
  })

  const title = document.createElement('h4')
  title.textContent = '选择或创建标签'
  title.style.position = 'relative'
  title.style.paddingRight = '30px'

  selector.appendChild(closeBtn)
  selector.appendChild(title)

  const tabContainer = document.createElement('div')
  tabContainer.className = 'tab-container'

  // 标签选择标签页
  const selectTab = document.createElement('div')
  selectTab.className = 'tab-content'

  const tagContainer = document.createElement('div')
  tagContainer.className = 'tag-container'
  tagContainer.style.maxHeight = '300px'
  tagContainer.style.overflowY = 'auto'

  // 按分类分组标签
  const tagGroups = {}
  Object.entries(tagsData).forEach(([tagId, tagName]) => {
    const groupName = tagName.includes(':') ? tagName.split(':')[0] : '其他'
    if (!tagGroups[groupName]) {
      tagGroups[groupName] = []
    }
    tagGroups[groupName].push({ tagId, tagName })
  })

  // 添加分组标签
  Object.entries(tagGroups).forEach(([groupName, tags]) => {
    // 添加分组标题
    const groupHeader = document.createElement('div')
    groupHeader.className = 'tag-group-header'
    groupHeader.textContent = groupName
    tagContainer.appendChild(groupHeader)

    // 添加分组标签
    const groupContainer = document.createElement('div')
    groupContainer.className = 'tag-group'

    tags.forEach(({ tagId, tagName }) => {
      const tag = document.createElement('div')
      tag.className = 'tag-option'
      tag.textContent = tagName.includes(':') ? tagName.split(':')[1] : tagName
      tag.dataset.tagId = tagId
      let 选取文件数量 = 0
      tag.addEventListener('click', () => {

        if (selectedFiles.size > 1) {
          选取文件数量 = selectedFiles.size
        }
        selectedFiles.forEach(selectedFile => {
          if (!fileTags[selectedFile]) {
            fileTags[selectedFile] = []
          }
          if (!fileTags[selectedFile].includes(tagId)) {
            fileTags[selectedFile].push(tagId)
            saveFileTags()
            //
            if (选取文件数量 > 1) {

              提示('已添加标签')
            } else {
              renderFileInfoPanel()
            }
          }
        })
        document.body.removeChild(selector)

      })
      groupContainer.appendChild(tag)
    })

    tagContainer.appendChild(groupContainer)
  })

  selectTab.appendChild(tagContainer)
  tabContainer.appendChild(selectTab)

  // 标签创建表单
  const createForm = document.createElement('div')
  createForm.className = 'tag-creation-form'

  const categorySelect = document.createElement('select')
  categorySelect.innerHTML = `
    <option value="">创建新标签，请先选择分类</option>
    <option value="自定义">新建分类</option>
  `
  let categories = Object.keys(tagsData).map(tagId => tagsData[tagId].split(':')[0])
  categories = [...new Set(categories)].filter(category => category)
  categories.sort()
  if (categories.length) categorySelect.innerHTML = `
      <option value="">创建新标签，请先选择分类</option>
    <option value="自定义">新建分类</option>`
  //categories.unshift('')
  categories.forEach(category => {
    categorySelect.innerHTML += `<option value="${category}">${category}</option>`
  })
  const tagNameInput = document.createElement('input')
  tagNameInput.placeholder = '标签名称'
  tagNameInput.style.display = 'none'
  const customCategoryInput = document.createElement('input')
  customCategoryInput.placeholder = '或输入新分类名称'
  customCategoryInput.style.display = 'none'

  categorySelect.addEventListener('change', (e) => {
    customCategoryInput.style.display = e.target.value === '自定义' ? 'block' : 'none'
    tagNameInput.style.display = e.target.value === '' ? 'none' : 'block'
  })



  const createBtn = document.createElement('button')
  createBtn.textContent = '创建并应用'
  createBtn.addEventListener('click', () => {
    let category = categorySelect.value
    if (category === '自定义') {
      category = customCategoryInput.value.trim()
    }

    const tagName = tagNameInput.value.trim()
    if (!tagName) return

    const fullTagName = category ? `${category}:${tagName}` : tagName

    const tagId = generateTagId()

    // 保存新标签到tags.json
    tagsData[tagId] = fullTagName
    saveTags()
    selectedFiles.forEach(selectedFile => {
      // 关联新标签到当前文件
      if (!fileTags[selectedFile]) {
        fileTags[selectedFile] = []
      }
      fileTags[selectedFile].push(tagId)
      saveFileTags()

      renderTagTree() // 刷新标签树
      renderFileInfoPanel()
    })
    document.body.removeChild(selector)
  })

  createForm.appendChild(categorySelect)
  createForm.appendChild(customCategoryInput)
  createForm.appendChild(tagNameInput)
  createForm.appendChild(createBtn)
  tabContainer.appendChild(createForm)

  selector.appendChild(tabContainer)
  document.body.appendChild(selector)
}

// 重命名标签
function renameTag(tagId) {
  const oldName = tagsData[tagId];
  const newName = prompt('输入新标签名称', oldName);


  function prompt(text, defaultText) {
    document.getElementById('表格').style.display = 'block';
    document.getElementById('prompt-text').textContent = text;
    document.getElementById('prompt-old').textContent = "原名" + defaultText;
    let 保存 = document.getElementById('提交');
    保存.onclick = function () {
      document.getElementById('表格').style.display = 'none';
      const newname = document.getElementById('prompt-text').value;
      //return newname.value;
      //如果新名字不等旧名字就改名
      if (newname.value != defaultText.split(":")[1]) {
        tagsData[tagId] = defaultText.split(":")[0] + ":" + newname;
        console.log(tagsData[tagId], defaultText);
        saveTags();
        renderTagTree();
        提示('已修改');
      } else {
        提示('未修改');
      }
    };
    let 取消 = document.getElementById('取消');
    取消.onclick = function () {
      document.getElementById('表格').style.display = 'none';

    }

  }
}

// 删除标签
function deleteTag(tagId) {
  if (!confirm(`确定要删除标签"${tagsData[tagId]}"吗？这将从所有文件中移除该标签。`)) return;

  // 从tags.json中删除
  delete tagsData[tagId];
  saveTags();

  // 从所有文件的标签中删除
  Object.keys(fileTags).forEach(filePath => {
    fileTags[filePath] = fileTags[filePath].filter(id => id !== tagId);
  });
  saveFileTags();

  renderTagTree();
}
const 搜索框 = document.getElementById('search-file');
const 搜索文件名按钮 = document.getElementById('search-file-name-btn');
const 搜索标签按钮 = document.getElementById('search-tag-btn');

//在当前文件列表中筛选文件名
搜索文件名按钮.addEventListener('click', () => {
  if (搜索框.value == '') {
    const thumbnails = document.querySelectorAll('.thumbnail-container');
    thumbnails.forEach(thumbnail => {
      thumbnail.style.display = 'block';
    });
    return;
  }
  //隐藏所有 Class=thumbnail-container的元素, 然后显示匹配的元素
  const thumbnails = document.querySelectorAll('.thumbnail-container');
  thumbnails.forEach(thumbnail => {
    thumbnail.style.display = 'none';
    if (thumbnail.dataset.path.includes(搜索框.value)) {
      thumbnail.style.display = 'block';
    }
  });
});
//在当前文件列表中筛选标签
搜索标签按钮.addEventListener('click', () => {
  const searchText = 搜索框.value.trim();
  if (searchText) searchAndFilterByTag(searchText);

});
// 在renderer.js中添加以下代码

// 标签搜索功能
function searchAndFilterByTag(searchText) {
  const mainContent = document.getElementById('main-content');
  const allThumbnails = Array.from(mainContent.querySelectorAll('.thumbnail-container'));

  // 1. 在tags.json中搜索匹配标签
  const matchedTagIds = Object.keys(tagsData)
    .filter(tagId => tagsData[tagId].toLowerCase().includes(searchText.toLowerCase()));

  if (matchedTagIds.length === 0) {
    提示('没有找到匹配的标签');
    return;
  }

  // 2. 隐藏所有文件，然后显示匹配的
  let hasMatches = false;
  allThumbnails.forEach(thumb => {
    const filePath = thumb.dataset.path;
    const isMatch = fileTags[filePath]?.some(tagId => matchedTagIds.includes(tagId));
    thumb.style.display = isMatch ? '' : 'none';
    if (isMatch) hasMatches = true;
  });

  if (!hasMatches) {
    提示('当前显示的文件中没有匹配该标签的');
  }

  document.getElementById('main-status').textContent = `标签搜索: ${searchText}`;
}

document.getElementById('清空').addEventListener('click', () => {
  const allThumbnails = Array.from(document.getElementById('main-content').querySelectorAll('.thumbnail-container'));
  搜索框.value = '';
  allThumbnails.forEach(thumb => {
    thumb.style.display = '';
  });
});

// 文件拖动功能实现

document.querySelectorAll('.thumbnail-container').forEach(thumbnail => {
  console.log('添加拖动事件:', thumbnail.dataset.path);

});

// 文件拖动功能实现

// 为文件夹树添加拖动接收事件
// 渲染缩略图
// 渲染缩略图
async function renderThumbnail2(filePath, targetContainer = null) {
  const ext = path.extname(filePath).toLowerCase()
  const mainContent = targetContainer || document.getElementById('file-container') || document.getElementById('main-content')

  // 创建缩略图容器
  const container = document.createElement('div')
  container.className = 'thumbnail-container'
  container.dataset.path = filePath
  container.dataset.file = filePath // 添加data-file属性用于拖拽
  container.draggable = true // 添加draggable属性

  // 创建缩略图元素
  const thumbnail = document.createElement('img')
  thumbnail.className = 'thumbnail'

  // 创建文件名元素
  const fileName = document.createElement('div')
  fileName.className = 'file-name'
  fileName.textContent = path.basename(filePath)

  // 确保缩略图目录存在
  ensureThumbDir()

  // 获取缩略图路径
  const thumbPath = getThumbPath(filePath)
  if (IMAGE_EXTS.includes(ext)) {
    if (!fs.existsSync(thumbPath)) {
      await generateThumbnail(filePath, thumbPath)
    }
    thumbnail.src = thumbPath
    thumbnail.alt = path.basename(filePath)
  } else if (ext === '.mp4' || ext === '.mov' || ext === '.avi' || ext === '.mkv') {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '📹'
  } else if (ext === '.ai' || ext === '.psd' || ext === '.psb' || ext === '.eps' || ext === '.cdr') {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '🎨'
  } else if (ext === '.html') {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '🌐'
  } else if (ext === '.pur') {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '🅟'
  } else {
    thumbnail.style.fontSize = '120px'
    thumbnail.src = thumbPath
    thumbnail.alt = '📄'
  }

  // 添加多选文件功能
  container.addEventListener('click', (e) => {
    const thumbnails = Array.from(document.querySelectorAll('.thumbnail-container'))
    const currentIndex = thumbnails.indexOf(container)

    if (e.ctrlKey || e.metaKey) { // 同时支持Ctrl和Mac的Cmd键
      // Ctrl/Cmd+点击：多选/取消选择
      container.classList.toggle('selected')
      if (container.classList.contains('selected')) {
        selectedFiles.add(filePath)
      } else {
        selectedFiles.delete(filePath)
      }
      //在右侧栏显示批量操作工具栏
      setTimeout(() => {
        if (selectedFiles.size > 1) {
          document.getElementById('file-info').innerHTML = `已选择 ${selectedFiles.size} 个文件`
          document.getElementsByClassName('tag-list')[0].style.display = 'none'
          //document.getElementById('right-sidebar').appendChild(document.getElementById('batch-toolbar'))
        }
      }, 100)
    } else if (e.shiftKey && lastSelectedIndex !== -1) {
      // Shift+点击：连续选择
      const start = Math.min(lastSelectedIndex, currentIndex)
      const end = Math.max(lastSelectedIndex, currentIndex)
      thumbnails.slice(start, end + 1).forEach(el => {
        el.classList.add('selected')
        selectedFiles.add(el.dataset.path)
      })
      //在右侧栏显示批量操作工具栏
      setTimeout(() => {
        if (selectedFiles.size > 1) {
          document.getElementById('file-info').innerHTML = `已选择 ${selectedFiles.size} 个文件`
          document.getElementsByClassName('tag-list')[0].style.display = 'none'
          //document.getElementById('right-sidebar').appendChild(document.getElementById('batch-toolbar'))
        }
      }, 100)
    } else {
      // 普通点击：单选
      document.querySelectorAll('.thumbnail-container').forEach(el => {
        el.classList.remove('selected')
      })
      container.classList.add('selected')
      selectedFiles = new Set([filePath])
      selectedFile = filePath
    }
    //console.log('selectedFiles', selectedFiles)
    renderFileInfoPanel()//更新右侧栏的文件信息面板
    lastSelectedIndex = currentIndex
    updateBatchToolbar()//更新批量操作工具栏
  })

  // 添加双击预览图片功能
  if (IMAGE_EXTS.includes(ext)) {
    container.addEventListener('dblclick', () => {
      openImagePreview(filePath)
    })
  }
  //双击文本格式预览功能
  if (ext === '.txt' || ext === '.md' || ext === '.json' || ext === '.xml' || ext === '.css' || ext === '.js') {
    container.addEventListener('dblclick', () => {
      openTextPreview(filePath)
    })
  }

  // 添加双击使用关联软件打开文件功能
  container.addEventListener('dblclick', () => {
    const softwareListData = JSON.parse(localStorage.getItem('softwareList')) || [];
    const matchingSoftware = softwareListData.find(software => {
      const formats = software.guanlian ? software.guanlian.split(' ') : [];
      return formats.includes(ext);
    });

    if (matchingSoftware) {
      openFileWithSoftware(matchingSoftware.path, filePath);
    } else {
      // alert('没有找到关联的软件来打开此文件');
    }
  });

  container.appendChild(thumbnail)
  container.appendChild(fileName)
  mainContent.appendChild(container)

  // 添加拖拽功能（保留原有的内部拖拽功能）
  //const { addDragEvents } = require('./src/dragManager');
  addDragEvents(container);//添加拖拽事件

  // 在函数末尾添加右键菜单
  addContextMenuToThumbnail(container, filePath);
}

// 为文件夹树添加拖动接收事件
function handleFolderDrop() {
  document.querySelectorAll('.folder-item').forEach(folder => {
    // 定义命名事件处理函数
    function handleDragStart2(e) {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
      folder.classList.add('drag-over');
    }

    function handleDragLeave2() {
      folder.classList.remove('drag-over');
    }

    async function handDragOver2(e) {
      var linshi = 0
      e.preventDefault();
      folder.classList.remove('drag-over');

      // 使用全局变量防止重复处理
      if (window.isDragProcessing) {
        console.log('正在处理拖拽，跳过重复操作');
        return;
      }

      console.log('handleDragOver2');

      // 安全地解析拖拽数据（只处理内部拖拽）
      let selectedFilePaths;
      try {
        // 只读取JSON格式的数据（内部拖拽）
        const dragData = e.dataTransfer.getData('application/json');
        console.log('内部拖拽数据:', dragData);

        if (!dragData || dragData.trim() === '') {
          console.log('没有内部拖拽数据，跳过处理');
          return;
        }
        if (linshi > 0) {
          return;
        }
        lins = linshi++
        // 解析JSON格式的数据
        selectedFilePaths = JSON.parse(dragData);
        console.log('成功解析内部拖拽数据:', selectedFilePaths);

        // 验证解析结果
        if (!Array.isArray(selectedFilePaths) || selectedFilePaths.length === 0) {
          console.log('拖拽数据不是有效的数组格式，跳过处理');
          return;
        }
      } catch (error) {
        console.error('解析内部拖拽数据失败:', error);
        console.log('原始拖拽数据:', e.dataTransfer.getData('application/json'));
        return;
      }

      const targetFolderPath = folder.dataset.path;

      if (selectedFilePaths && targetFolderPath) {
        // 设置全局处理标志，防止重复处理
        window.isDragProcessing = true;

        try {
          let successCount = 0;
          let skipCount = 0;
          let errorCount = 0;

          // 检查是否需要确认覆盖（只检查一次）
          const conflictFiles = [];

          // 先检查所有文件是否有冲突
          for (const filePath of selectedFilePaths) {
            const fileName = path.basename(filePath);
            const newPath = path.join(targetFolderPath, fileName);

            // 检查是否是移动到相同目录
            if (path.dirname(filePath) === targetFolderPath) {
              console.log('文件已在目标目录中:', fileName);
              skipCount++;
              continue;
            }

            // 检查目标文件是否已存在
            if (fs.existsSync(newPath)) {
              conflictFiles.push(fileName);
            }
          }

          // 如果有冲突文件，询问用户
          if (conflictFiles.length > 0) {
            const message = conflictFiles.length === 1
              ? `文件 "${conflictFiles[0]}" 已存在，是否覆盖？`
              : `有 ${conflictFiles.length} 个文件已存在，是否全部覆盖？`;

            const confirm = await ipcRenderer.invoke('show-confirm-dialog', {
              message: message,
              buttons: ['覆盖', '取消']
            });

            if (confirm.response === 1) { // 取消
              console.log('用户取消了覆盖操作');
              return;
            }
          }

          // 处理所有文件
          for (const filePath of selectedFilePaths) {
            const fileName = path.basename(filePath);
            const newPath = path.join(targetFolderPath, fileName);

            // 检查是否是移动到相同目录
            if (path.dirname(filePath) === targetFolderPath) {
              continue; // 已在上面统计过
            }

            // 检查源文件是否存在
            if (!fs.existsSync(filePath)) {
              console.log('源文件不存在，跳过:', fileName);
              continue;
            }

            try {
              // 移动文件
              const result = await ipcRenderer.invoke('move-file', {
                oldPath: filePath,
                newPath: newPath
              });

              if (result.success) {
                console.log('移动成功:', fileName);
                successCount++;

                // 更新info.json中的路径
                if (fileTags[filePath]) {
                  fileTags[newPath] = fileTags[filePath];
                  delete fileTags[filePath];
                }

                // 更新webinfo.json中的路径
                if (fileWebInfo[filePath]) {
                  fileWebInfo[newPath] = fileWebInfo[filePath];
                  delete fileWebInfo[filePath];
                }
              } else {
                console.error('移动文件失败:', fileName, result.error);
                errorCount++;
              }
            } catch (error) {
              console.error('移动文件失败:', fileName, error);
              errorCount++;
            }
          }

          // 保存更新的标签和网址信息
          if (successCount > 0) {
            saveFileTags();
            saveFileWebInfo();
          }

          // 显示结果提示
          if (successCount > 0) {
            const message = `成功移动 ${successCount} 个文件` +
              (skipCount > 0 ? `，跳过 ${skipCount} 个文件` : '') +
              (errorCount > 0 ? `，失败 ${errorCount} 个文件` : '');
            console.log(message);

            // 刷新界面
            loadFolderContents(targetFolderPath);

            // 移除文件夹树的active状态
            document.querySelectorAll('.folder-item').forEach(item => {
              item.classList.remove('active');
            });
            // 激活当前文件夹
            folder.classList.add('active');
            // 更新状态栏文件夹路径
            document.getElementById('main-status').textContent = `当前文件夹: ${targetFolderPath}`;
          }

        } finally {
          // 清除全局处理标志
          window.isDragProcessing = false;
        }
      }
    }

    // 清理旧事件监听器
    folder.removeEventListener('dragover', handleDragStart2);
    folder.removeEventListener('dragleave', handleDragLeave2);
    folder.removeEventListener('drop', handDragOver2);

    // 绑定新事件监听器
    folder.addEventListener('dragover', handleDragStart2);
    folder.addEventListener('dragleave', handleDragLeave2);
    folder.addEventListener('drop', handDragOver2);
  });
}


async function handlePasteThumbnail() {
  // 尝试从剪贴板读取图片
  const image = clipboard.readImage();
  if (image.isEmpty()) {
    alert('剪贴板中没有图片数据');
    return;
  }

  // 将图片转换为 Buffer
  const buffer = image.toJPEG(80); // 转换为 JPEG 格式，质量为 80
  const hash = require('crypto').createHash('md5').update(buffer).digest('hex');
  const customThumbPath = path.join('CustomThum', `${hash}.jpg`);

  // 确保 CustomThum 目录存在
  const customThumDir = 'CustomThum';
  if (!fs.existsSync(customThumDir)) {
    fs.mkdirSync(customThumDir);
  }

  // 保存图片到自定义缩略图目录
  fs.writeFileSync(customThumbPath, buffer);

  // 更新缩略图
  const thumbPath = getThumbPath(selectedFile);
  fs.writeFileSync(thumbPath, buffer);

  // 更新 CustomThum.json 记录
  const customThumJsonPath = 'CustomThum.json';
  let customThumData = {};
  if (fs.existsSync(customThumJsonPath)) {
    customThumData = JSON.parse(fs.readFileSync(customThumJsonPath, 'utf-8'));
  }
  customThumData[selectedFile] = customThumbPath;
  fs.writeFileSync(customThumJsonPath, JSON.stringify(customThumData, null, 2));

  // 重新渲染缩略图
  renderThumbnail2(selectedFile);
  alert('缩略图已替换');
}
// 添加清理缓存按钮
const settingsPanel = document.getElementById('modal-content') // 假设有一个设置面板
if (settingsPanel) {
  const clearCacheButton = document.createElement('button')
  clearCacheButton.textContent = '清理缓存'
  clearCacheButton.className ='t2'
  clearCacheButton.addEventListener('click', clearUnusedThumbnails)
  settingsPanel.appendChild(clearCacheButton)
}


document.addEventListener('DOMContentLoaded', () => {
  const leftSidebar = document.getElementById('left-sidebar');
  const rightSidebar = document.getElementById('right-sidebar');
  const tagTree = document.getElementById('tag-tree');
  const 折叠 = document.getElementById('折叠');
  const leftBar = document.getElementById('left-bar');
  const rightBar = document.getElementById('right-bar');

  // 从 localStorage 加载侧边栏宽度
  const leftWidth = parseInt(localStorage.getItem('leftSidebarWidth')) || 250;
  const rightWidth = parseInt(localStorage.getItem('rightSidebarWidth')) || 250;
  const tagHeight = parseInt(localStorage.getItem('tagHeight')) || 250;

  leftSidebar.style.width = `${leftWidth}px`;
  rightSidebar.style.width = `${rightWidth}px`;
  tagTree.style.height = `${tagHeight}px`;

  // 左侧栏拖拽逻辑
  let isLeftDragging = false;
  let startX = 0;
  let startLeftWidth = 0;

  leftBar.addEventListener('mousedown', (e) => {
    isLeftDragging = true;
    startX = e.clientX;
    startLeftWidth = leftSidebar.offsetWidth;
    document.addEventListener('mousemove', onLeftMouseMove);
    document.addEventListener('mouseup', onLeftMouseUp);
  });

  function onLeftMouseMove(e) {
    if (!isLeftDragging) return;
    const deltaX = e.clientX - startX;
    const newWidth = startLeftWidth + deltaX;
    if (newWidth > 100 && newWidth < window.innerWidth - 300) { // 限制最小宽度为 100px
      leftSidebar.style.width = `${newWidth}px`;
      localStorage.setItem('leftSidebarWidth', newWidth);
    }
  }

  function onLeftMouseUp() {
    isLeftDragging = false;
    document.removeEventListener('mousemove', onLeftMouseMove);
    document.removeEventListener('mouseup', onLeftMouseUp);
  }

  // 右侧栏拖拽逻辑
  let isRightDragging = false;
  let startRightX = 0;
  let startRightWidth = 0;

  rightBar.addEventListener('mousedown', (e) => {
    isRightDragging = true;
    startRightX = e.clientX;
    startRightWidth = rightSidebar.offsetWidth;
    document.addEventListener('mousemove', onRightMouseMove);
    document.addEventListener('mouseup', onRightMouseUp);
  });

  function onRightMouseMove(e) {
    if (!isRightDragging) return;
    const deltaX = e.clientX - startRightX;
    const newWidth = startRightWidth - deltaX;
    if (newWidth > 100 && newWidth < window.innerWidth - 300) { // 限制最小宽度为 100px
      rightSidebar.style.width = `${newWidth}px`;
      localStorage.setItem('rightSidebarWidth', newWidth);
    }
  }

  function onRightMouseUp() {
    isRightDragging = false;
    document.removeEventListener('mousemove', onRightMouseMove);
    document.removeEventListener('mouseup', onRightMouseUp);
  }

  // 右侧栏拖拽逻辑------------
  let isUPDragging = false;
  let startUPY = 0;
  let startUpHeight = 0;

  折叠.addEventListener('mousedown', (e) => {
    isUPDragging = true;
    startUPY = e.clientY;
    startUpHeight = tagTree.offsetHeight;
    document.addEventListener('mousemove', onUPMouseMove);
    document.addEventListener('mouseup', onUPMouseUp);
  });

  function onUPMouseMove(e) {
    if (!isUPDragging) return;
    const deltaY = e.clientY - startUPY;
    const newHeight = startUpHeight - deltaY;
    if (newHeight > 100 && newHeight < window.innerHeight - 300) { // 限制最小宽度为 100px
      tagTree.style.height = `${newHeight}px`;
      localStorage.setItem('tagHeight', newHeight);
    }
  }

  function onUPMouseUp() {
    isUPDragging = false;
    document.removeEventListener('mousemove', onUPMouseMove);
    document.removeEventListener('mouseup', onUPMouseUp);
  }
});
document.addEventListener('DOMContentLoaded', () => {
  let folder = document.getElementById('main-status').textContent;
  const 粘贴png = document.getElementById('粘贴png');
  const 粘贴jpg = document.getElementById('粘贴jpg');
  console.log(folder, 筛选状态);

  // 添加全局拖拽功能
  setupGlobalDragAndDrop();

  粘贴png.addEventListener('click', async () => {
    folder = document.getElementById('main-status').textContent;
    try {
      // 检查是否有权限读取剪贴板
      if (!navigator.clipboard) {
        alert('当前环境不支持读取剪贴板');
        return;
      }

      const clipboardData = await navigator.clipboard.read();
      console.log('Clipboard data:', clipboardData);

      if (筛选状态 === '文件夹') {
        let foundImage = false;
        for (let item of clipboardData) {
          console.log('Clipboard item:', item);
          if (item.types.includes('image/png') || item.types.includes('image/jpeg')) {
            const blob = await item.getType('image/png') || await item.getType('image/jpeg');
            const arrayBuffer = await blob.arrayBuffer();
            const buffer = Buffer.from(arrayBuffer);
            const filePath = `${folder}/${Date.now()}.png`;
            fs.writeFileSync(filePath, buffer);
            提示('图片已保存为 PNG 格式，需要手动点击刷新查看结果');
            foundImage = true;
            // 刷新文件夹
            const activeFolder = document.querySelector('.folder-item.active');
            if (activeFolder) {
              const folderPath = activeFolder.dataset.path;
              loadFolderContents(folderPath);
            }
            break; // 保存一张图片后退出循环
          }
        }
        if (!foundImage) {
          提示('剪贴板中未找到图像数据');
        }
      } else {
        提示('当前状态不是文件夹，无法保存图片');
      }
    } catch (error) {
      console.error('读取剪贴板失败:', error);
      提示('读取剪贴板失败，请检查权限或环境');
    }
  });
  粘贴jpg.addEventListener('click', async () => {
    folder = document.getElementById('main-status').textContent;
    try {
        // 检查是否有权限读取剪贴板
        if (!navigator.clipboard) {
            alert('当前环境不支持读取剪贴板');
            return;
        }

        const clipboardData = await navigator.clipboard.read();
        console.log('Clipboard data:', clipboardData);

        if (筛选状态 === '文件夹') {
            let foundImage = false;
            for (let item of clipboardData) {
                console.log('Clipboard item:', item);
                if (item.types.includes('image/png') || item.types.includes('image/jpeg')) {
                    const blob = await item.getType('image/png') || await item.getType('image/jpeg');
                    
                    // 创建临时图片对象用于转换格式
                    const img = new Image();
                    img.src = URL.createObjectURL(blob);
                    await new Promise(resolve => img.onload = resolve); // 等待图片加载完成

                    // 使用 canvas 转换为 JPG 并压缩至 70%
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    
                    // 导出为 jpg 格式，质量 0.7
                    const jpgBlob = await new Promise(resolve => {
                        canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.7);
                    });

                    // 将 Blob 转换为 Buffer 以便写入文件
                    const arrayBuffer = await jpgBlob.arrayBuffer();
                    const buffer = Buffer.from(arrayBuffer);

                    // 保存为 .jpg 文件
                    const filePath = `${folder}/${Date.now()}.jpg`;
                    fs.writeFileSync(filePath, buffer);
                    提示('图片已保存为 JPG 格式（质量 70%），需要手动点击刷新查看结果');
                    foundImage = true;

                    // 刷新文件夹内容
                    const activeFolder = document.querySelector('.folder-item.active');
                    if (activeFolder) {
                        const folderPath = activeFolder.dataset.path;
                        loadFolderContents(folderPath);
                    }
                    break; // 保存一张图片后退出循环
                }
            }
            if (!foundImage) {
                提示('剪贴板中未找到图像数据');
            }
        } else {
            提示('当前状态不是文件夹，无法保存图片');
        }
    } catch (error) {
        console.error('读取剪贴板失败:', error);
        提示('读取剪贴板失败，请检查权限或环境');
    }
});
});
  
async function clearUnusedThumbnails() {
  const thumbDir = 'thum'
  const customThumJsonPath = 'CustomThum.json'
  const customThumData = fs.existsSync(customThumJsonPath) ? JSON.parse(fs.readFileSync(customThumJsonPath, 'utf-8')) : {}

  const files = fs.readdirSync(thumbDir)
  const thumbPaths = files.map(file => path.join(thumbDir, file))

  const usedThumbs = new Set()
  for (const filePath in customThumData) {
    usedThumbs.add(customThumData[filePath])
  }

  thumbPaths.forEach(thumbPath => {
    if (!usedThumbs.has(thumbPath)) {
      fs.unlinkSync(thumbPath)
      console.log(`已删除未使用的缩略图: ${thumbPath}`)
    }
  })

  alert('缓存已清理')
}

// 为缩略图添加右键菜单
function addContextMenuToThumbnail(container, filePath) {
  container.addEventListener('contextmenu', (e) => {
    e.preventDefault();

    // 如果右键的文件不在选中列表中，则选中它
    if (!selectedFiles.has(filePath)) {
      // 清除其他选择
      document.querySelectorAll('.thumbnail-container').forEach(el => {
        el.classList.remove('selected');
      });
      selectedFiles.clear();

      // 选中当前文件
      container.classList.add('selected');
      selectedFiles.add(filePath);
      selectedFile = filePath;
    }

    // 移除现有菜单
    const oldMenu = document.querySelector('.file-context-menu');
    if (oldMenu) oldMenu.remove();

    // 创建菜单
    const menu = document.createElement('div');
    menu.className = 'file-context-menu';
    menu.style.position = 'fixed';
    menu.style.left = `${e.clientX}px`;
    menu.style.top = `${e.clientY}px`;
    menu.style.background = 'white';
    menu.style.border = '1px solid #ddd';
    menu.style.borderRadius = '4px';
    menu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
    menu.style.zIndex = '1000';
    menu.style.minWidth = '150px';

    const selectedCount = selectedFiles.size;

    // 添加菜单项 - 重命名
    const renameOption = document.createElement('div');
    renameOption.textContent = selectedCount > 1 ? `批量重命名 (${selectedCount}个文件)` : '重命名';
    renameOption.style.padding = '8px 16px';
    renameOption.style.cursor = 'pointer';
    renameOption.onclick = (e) => {
      e.stopPropagation();
      menu.remove();
      if (selectedCount > 1) {
        showBatchRenameDialog();
      } else {
        renameFile(filePath);
      }
    };

    // 添加菜单项 - 删除
    const deleteOption = document.createElement('div');
    deleteOption.textContent = selectedCount > 1 ? `删除 (${selectedCount}个文件)` : '删除';
    deleteOption.style.padding = '8px 16px';
    deleteOption.style.cursor = 'pointer';
    deleteOption.style.color = 'red';
    deleteOption.onclick = (e) => {
      e.stopPropagation();
      menu.remove();
      if (selectedCount > 1) {
        batchDeleteFiles();
      } else {
        deleteFile(filePath);
      }
    };

    // 添加菜单项 - 复制文件
    const copyFileOption = document.createElement('div');
    copyFileOption.textContent = selectedCount > 1 ? `复制文件 (${selectedCount}个文件)` : '复制文件';
    copyFileOption.style.padding = '8px 16px';
    copyFileOption.style.cursor = 'pointer';
    copyFileOption.onclick = (e) => {
      e.stopPropagation();
      menu.remove();
      copyFilesToClipboard();
    };

    // 添加菜单项 - 在资源管理器中打开
    const openInExplorerOption = document.createElement('div');
    openInExplorerOption.textContent = '在资源管理器中打开';
    openInExplorerOption.style.padding = '8px 16px';
    openInExplorerOption.style.cursor = 'pointer';
    openInExplorerOption.onclick = (e) => {
      e.stopPropagation();
      menu.remove();
      openFileInExplorer(filePath);
    };

    menu.appendChild(renameOption);
    menu.appendChild(deleteOption);
    menu.appendChild(copyFileOption);
    menu.appendChild(openInExplorerOption);
    document.body.appendChild(menu);

    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
      if (!menu.contains(e.target)) {
        menu.remove();
        document.removeEventListener('click', closeMenu);
      }
    };
    setTimeout(() => document.addEventListener('click', closeMenu), 100);
  });
}

// 重命名文件
// 重命名文件
function renameFile(filePath) {
  (async () => {
    const fileName = path.basename(filePath);
    // 使用我们自定义的prompt函数，而不是window.prompt
    const newName = await prompt("请输入新文件名:", fileName);
    if (newName && newName !== fileName) {
      const dirPath = path.dirname(filePath);
      const newPath = path.join(dirPath, newName);

      try {
        // 检查文件是否已存在
        if (fs.existsSync(newPath)) {
          const confirm = await ipcRenderer.invoke('show-confirm-dialog', {
            message: `文件 "${newName}" 已存在，是否覆盖？`,
            buttons: ['覆盖', '取消']
          });
          if (confirm.response === 1) return; // 取消
        }

        // 重命名文件
        fs.renameSync(filePath, newPath);

        // 更新 info.json
        updateInfoJsonForRename(filePath, newPath);

        // 刷新文件夹
        const activeFolder = document.querySelector('.folder-item.active');
        if (activeFolder) {
          const folderPath = activeFolder.dataset.path;
          loadFolderContents(folderPath);
        }

        提示('文件已重命名');
      } catch (error) {
        console.error('重命名文件失败:', error);
        提示('重命名文件失败: ' + error.message);
      }
    }
  })();
}

// 删除文件
function deleteFile(filePath) {
  console.log('deleteFile 函数被调用，文件路径:', filePath);
  (async () => {
    try {
      const confirm = await ipcRenderer.invoke('show-confirm-dialog', {
        message: `确定要删除文件 "${path.basename(filePath)}" 吗？`,
        buttons: ['删除', '取消']
      });
      console.log('确认对话框结果:', confirm);

      if (confirm.response === 0) { // 确认删除
        try {
          // 删除文件
          fs.unlinkSync(filePath);

          // 更新 info.json
          updateInfoJsonForDelete(filePath);

          // 刷新文件夹
          const activeFolder = document.querySelector('.folder-item.active');
          if (activeFolder) {
            const folderPath = activeFolder.dataset.path;
            loadFolderContents(folderPath);
          }

          提示('文件已删除');
        } catch (error) {
          console.error('删除文件失败:', error);
          提示('删除文件失败: ' + error.message);
        }
      }
    } catch (error) {
      console.error('删除文件对话框错误:', error);
      提示('删除操作失败: ' + error.message);
    }
  })();
}

// 批量删除文件
function batchDeleteFiles() {
  console.log('批量删除文件，选中文件数量:', selectedFiles.size);
  (async () => {
    try {
      const fileList = Array.from(selectedFiles).map(fp => path.basename(fp)).join('\n');
      const confirm = await ipcRenderer.invoke('show-confirm-dialog', {
        message: `确定要删除以下 ${selectedFiles.size} 个文件吗？\n\n${fileList}`,
        buttons: ['删除', '取消']
      });
      console.log('批量删除确认对话框结果:', confirm);

      if (confirm.response === 0) { // 确认删除
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (const filePath of selectedFiles) {
          try {
            // 删除文件
            fs.unlinkSync(filePath);

            // 更新 info.json
            updateInfoJsonForDelete(filePath);

            // 从选中列表中移除
            selectedFiles.delete(filePath);

            // 从DOM中移除
            const container = document.querySelector(`.thumbnail-container[data-path="${filePath}"]`);
            if (container) {
              container.remove();
            }

            successCount++;
          } catch (error) {
            console.error('删除文件失败:', filePath, error);
            errors.push(`${path.basename(filePath)}: ${error.message}`);
            errorCount++;
          }
        }

        // 清空选中状态
        selectedFiles.clear();
        updateBatchToolbar();

        // 显示结果
        if (successCount > 0 && errorCount === 0) {
          提示(`成功删除 ${successCount} 个文件`);
        } else if (successCount > 0 && errorCount > 0) {
          提示(`成功删除 ${successCount} 个文件，${errorCount} 个文件删除失败`);
        } else {
          提示(`删除失败：\n${errors.join('\n')}`);
        }
      }
    } catch (error) {
      console.error('批量删除文件对话框错误:', error);
      提示('删除操作失败: ' + error.message);
    }
  })();
}

// 显示批量重命名对话框
function showBatchRenameDialog() {
  const modal = document.getElementById('batch-rename-modal');
  const ruleSelect = document.getElementById('rename-rule');
  const optionsDiv = document.getElementById('rename-options');
  const previewDiv = document.getElementById('rename-preview');

  modal.style.display = 'block';

  // 更新选项区域
  function updateOptions() {
    const rule = ruleSelect.value;
    optionsDiv.innerHTML = '';

    switch (rule) {
      case 'prefix':
        optionsDiv.innerHTML = `
          <label>前缀文本：</label>
          <input type="text" id="prefix-text" placeholder="输入前缀" style="width: 100%; padding: 5px; margin-top: 5px;">
        `;
        break;
      case 'suffix':
        optionsDiv.innerHTML = `
          <label>后缀文本：</label>
          <input type="text" id="suffix-text" placeholder="输入后缀" style="width: 100%; padding: 5px; margin-top: 5px;">
        `;
        break;
      case 'replace':
        optionsDiv.innerHTML = `
          <label>查找文本：</label>
          <input type="text" id="find-text" placeholder="要替换的文本" style="width: 100%; padding: 5px; margin-top: 5px;">
          <label style="margin-top: 10px; display: block;">替换为：</label>
          <input type="text" id="replace-text" placeholder="替换后的文本" style="width: 100%; padding: 5px; margin-top: 5px;">
        `;
        break;
      case 'sequence':
        optionsDiv.innerHTML = `
          <label>基础名称：</label>
          <input type="text" id="base-name" placeholder="文件" style="width: 100%; padding: 5px; margin-top: 5px;">
          <label style="margin-top: 10px; display: block;">起始序号：</label>
          <input type="number" id="start-number" value="1" min="1" style="width: 100%; padding: 5px; margin-top: 5px;">
          <label style="margin-top: 10px; display: block;">序号位数：</label>
          <input type="number" id="number-digits" value="3" min="1" max="6" style="width: 100%; padding: 5px; margin-top: 5px;">
        `;
        break;
      case 'case':
        optionsDiv.innerHTML = `
          <label>转换方式：</label>
          <select id="case-type" style="width: 100%; padding: 5px; margin-top: 5px;">
            <option value="upper">转为大写</option>
            <option value="lower">转为小写</option>
            <option value="title">首字母大写</option>
          </select>
        `;
        break;
    }

    // 绑定输入事件
    optionsDiv.querySelectorAll('input, select').forEach(input => {
      input.addEventListener('input', updatePreview);
    });

    updatePreview();
  }

  // 更新预览
  function updatePreview() {
    const rule = ruleSelect.value;
    const files = Array.from(selectedFiles);
    let preview = '';

    files.forEach((filePath, index) => {
      const fileName = path.basename(filePath);
      const ext = path.extname(fileName);
      const nameWithoutExt = path.basename(fileName, ext);
      let newName = fileName;

      try {
        switch (rule) {
          case 'prefix':
            const prefix = document.getElementById('prefix-text')?.value || '';
            newName = prefix + fileName;
            break;
          case 'suffix':
            const suffix = document.getElementById('suffix-text')?.value || '';
            newName = nameWithoutExt + suffix + ext;
            break;
          case 'replace':
            const findText = document.getElementById('find-text')?.value || '';
            const replaceText = document.getElementById('replace-text')?.value || '';
            if (findText) {
              newName = fileName.replace(new RegExp(findText, 'g'), replaceText);
            }
            break;
          case 'sequence':
            const baseName = document.getElementById('base-name')?.value || '文件';
            const startNumber = parseInt(document.getElementById('start-number')?.value || '1');
            const digits = parseInt(document.getElementById('number-digits')?.value || '3');
            const number = (startNumber + index).toString().padStart(digits, '0');
            newName = `${baseName}${number}${ext}`;
            break;
          case 'case':
            const caseType = document.getElementById('case-type')?.value || 'lower';
            switch (caseType) {
              case 'upper':
                newName = nameWithoutExt.toUpperCase() + ext;
                break;
              case 'lower':
                newName = nameWithoutExt.toLowerCase() + ext;
                break;
              case 'title':
                newName = nameWithoutExt.charAt(0).toUpperCase() + nameWithoutExt.slice(1).toLowerCase() + ext;
                break;
            }
            break;
        }
      } catch (error) {
        newName = fileName; // 如果出错，保持原名
      }

      preview += `${fileName} → ${newName}\n`;
    });

    previewDiv.textContent = preview;
  }

  // 绑定事件
  ruleSelect.addEventListener('change', updateOptions);

  document.getElementById('batch-rename-cancel').onclick = () => {
    modal.style.display = 'none';
  };

  document.getElementById('batch-rename-confirm').onclick = () => {
    executeBatchRename();
    modal.style.display = 'none';
  };

  // 初始化
  updateOptions();
}

// 执行批量重命名
function executeBatchRename() {
  const rule = document.getElementById('rename-rule').value;
  const files = Array.from(selectedFiles);
  let successCount = 0;
  let errorCount = 0;
  const errors = [];

  files.forEach((filePath, index) => {
    try {
      const fileName = path.basename(filePath);
      const ext = path.extname(fileName);
      const nameWithoutExt = path.basename(fileName, ext);
      const dirPath = path.dirname(filePath);
      let newName = fileName;

      // 根据规则生成新文件名
      switch (rule) {
        case 'prefix':
          const prefix = document.getElementById('prefix-text').value || '';
          newName = prefix + fileName;
          break;
        case 'suffix':
          const suffix = document.getElementById('suffix-text').value || '';
          newName = nameWithoutExt + suffix + ext;
          break;
        case 'replace':
          const findText = document.getElementById('find-text').value || '';
          const replaceText = document.getElementById('replace-text').value || '';
          if (findText) {
            newName = fileName.replace(new RegExp(findText, 'g'), replaceText);
          }
          break;
        case 'sequence':
          const baseName = document.getElementById('base-name').value || '文件';
          const startNumber = parseInt(document.getElementById('start-number').value || '1');
          const digits = parseInt(document.getElementById('number-digits').value || '3');
          const number = (startNumber + index).toString().padStart(digits, '0');
          newName = `${baseName}${number}${ext}`;
          break;
        case 'case':
          const caseType = document.getElementById('case-type').value || 'lower';
          switch (caseType) {
            case 'upper':
              newName = nameWithoutExt.toUpperCase() + ext;
              break;
            case 'lower':
              newName = nameWithoutExt.toLowerCase() + ext;
              break;
            case 'title':
              newName = nameWithoutExt.charAt(0).toUpperCase() + nameWithoutExt.slice(1).toLowerCase() + ext;
              break;
          }
          break;
      }

      // 如果新文件名与原文件名相同，跳过
      if (newName === fileName) {
        return;
      }

      const newPath = path.join(dirPath, newName);

      // 检查新文件名是否已存在
      if (fs.existsSync(newPath)) {
        errors.push(`${fileName}: 目标文件名已存在`);
        errorCount++;
        return;
      }

      // 执行重命名
      fs.renameSync(filePath, newPath);

      // 更新 info.json
      updateInfoJsonForRename(filePath, newPath);

      // 更新选中文件列表
      selectedFiles.delete(filePath);
      selectedFiles.add(newPath);

      // 更新DOM中的数据属性
      const container = document.querySelector(`.thumbnail-container[data-path="${filePath}"]`);
      if (container) {
        container.dataset.path = newPath;
        const fileNameElement = container.querySelector('.thumbnail-name');
        if (fileNameElement) {
          fileNameElement.textContent = newName;
        }
      }

      successCount++;
    } catch (error) {
      console.error('重命名文件失败:', filePath, error);
      errors.push(`${path.basename(filePath)}: ${error.message}`);
      errorCount++;
    }
  });

  // 刷新文件夹显示
  const activeFolder = document.querySelector('.folder-item.active');
  if (activeFolder) {
    const folderPath = activeFolder.dataset.path;
    loadFolderContents(folderPath);
  }

  // 显示结果
  if (successCount > 0 && errorCount === 0) {
    提示(`成功重命名 ${successCount} 个文件`);
  } else if (successCount > 0 && errorCount > 0) {
    提示(`成功重命名 ${successCount} 个文件，${errorCount} 个文件重命名失败`);
  } else if (errorCount > 0) {
    提示(`重命名失败：\n${errors.join('\n')}`);
  }
}

// 复制文件到剪贴板
function copyFilesToClipboard() {
  const filePaths = Array.from(selectedFiles);

  console.log('复制文件到剪贴板:', filePaths);

  try {
    // 使用IPC调用主进程来复制文件
    ipcRenderer.invoke('copy-files-to-clipboard', filePaths)
      .then((result) => {
        if (result.success) {
          提示(`已复制 ${filePaths.length} 个文件到剪贴板`);
        } else {
          提示('复制文件失败: ' + result.error);
        }
      })
      .catch((error) => {
        console.error('复制文件到剪贴板失败:', error);
        提示('复制文件失败: ' + error.message);
      });
  } catch (error) {
    console.error('复制文件到剪贴板失败:', error);
    提示('复制文件失败: ' + error.message);
  }
}

// 在资源管理器中打开文件
function openFileInExplorer(filePath) {
  const { shell } = require('electron');
  shell.showItemInFolder(filePath);
}

// 设置全局拖拽功能
function setupGlobalDragAndDrop() {
  let dragCounter = 0;

  // 防止默认的拖拽行为
  document.addEventListener('dragover', (e) => {
    e.preventDefault();
    e.stopPropagation();
  });

  document.addEventListener('dragenter', (e) => {
    e.preventDefault();
    e.stopPropagation();

    // 只在从外部拖拽文件时显示提示
    if (e.dataTransfer.types.includes('Files')) {
      dragCounter++;
      if (dragCounter === 1) {
        document.body.classList.add('drag-active');
      }
    }
  });

  document.addEventListener('dragleave', (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.types.includes('Files')) {
      dragCounter--;
      if (dragCounter === 0) {
        document.body.classList.remove('drag-active');
      }
    }
  });

  // 处理文件拖拽到应用界面
  document.addEventListener('drop', async (e) => {
    e.preventDefault();
    e.stopPropagation();

    // 清理拖拽状态
    dragCounter = 0;
    document.body.classList.remove('drag-active');

    // 检查当前是否为文件夹筛选模式
    if (筛选状态 !== '文件夹') {
      提示('无法复制文件，请先选择一个文件夹');
      return;
    }

    // 获取当前打开的文件夹路径
    const currentFolderPath = document.getElementById('main-status').textContent;
    if (!currentFolderPath || currentFolderPath === '文件夹状态') {
      提示('请先选择一个文件夹');
      return;
    }

    // 检查是否有拖拽的文件
    if (!e.dataTransfer.files || e.dataTransfer.files.length === 0) {
      return;
    }

    const files = Array.from(e.dataTransfer.files);
    let copiedCount = 0;
    let errorCount = 0;

    for (const file of files) {
      try {
        // 检查是否为图片文件
        /*const ext = path.extname(file.name).toLowerCase();
        if (!IMAGE_EXTS.includes(ext)) {
          console.log(`跳过非图片文件: ${file.name}`);
          continue;
        }*/

        // 构建目标路径
        const targetPath = path.join(currentFolderPath, file.name);

        // 检查文件是否已存在
        if (fs.existsSync(targetPath)) {
          const confirm = await ipcRenderer.invoke('show-confirm-dialog', {
            message: `文件 "${file.name}" 已存在，是否覆盖？`,
            buttons: ['覆盖', '跳过', '取消']
          });

          if (confirm.response === 2) { // 取消
            break;
          } else if (confirm.response === 1) { // 跳过
            continue;
          }
          // response === 0 表示覆盖，继续执行
        }

        // 复制文件
        const result = await ipcRenderer.invoke('copy-file', {
          sourcePath: file.path,
          targetPath: targetPath
        });

        if (result.success) {
          copiedCount++;
        } else {
          errorCount++;
          console.error(`复制文件失败: ${file.name}`, result.error);
        }
      } catch (error) {
        errorCount++;
        console.error(`处理文件失败: ${file.name}`, error);
      }
    }

    // 显示结果提示
    if (copiedCount > 0) {
      提示(`成功复制 ${copiedCount} 个图片文件${errorCount > 0 ? `，${errorCount} 个文件失败` : ''}`);

      // 刷新当前文件夹显示
      const activeFolder = document.querySelector('.folder-item.active');
      if (activeFolder) {
        loadFolderContents(currentFolderPath);
      }
    } else if (errorCount > 0) {
      提示(`复制失败，${errorCount} 个文件处理失败`);
    } else {
      提示('没有找到可复制的图片文件');
    }
  });
}

// 更新 info.json 文件（重命名）
function updateInfoJsonForRename(oldPath, newPath) {
  if (!window.rootFolderPath) return;

  const infoPath = path.join(window.rootFolderPath, 'info.json');
  if (!fs.existsSync(infoPath)) return;

  try {
    // 读取 info.json
    const infoData = JSON.parse(fs.readFileSync(infoPath, 'utf-8'));

    // 计算相对路径
    const oldRelativePath = path.relative(window.rootFolderPath, oldPath).replace(/\\/g, '/');
    const newRelativePath = path.relative(window.rootFolderPath, newPath).replace(/\\/g, '/');

    // 如果旧路径存在于 info.json 中，则更新
    if (infoData[oldRelativePath]) {
      infoData[newRelativePath] = infoData[oldRelativePath];
      delete infoData[oldRelativePath];

      // 保存更新后的 info.json
      fs.writeFileSync(infoPath, JSON.stringify(infoData, null, 2));
      console.log('info.json 已更新（重命名）');
    }
  } catch (error) {
    console.error('更新 info.json 失败（重命名）:', error);
  }
}

// 更新 info.json 文件（删除）
function updateInfoJsonForDelete(filePath) {
  if (!window.rootFolderPath) return;

  const infoPath = path.join(window.rootFolderPath, 'info.json');
  if (!fs.existsSync(infoPath)) return;

  try {
    // 读取 info.json
    const infoData = JSON.parse(fs.readFileSync(infoPath, 'utf-8'));

    // 计算相对路径
    const relativePath = path.relative(window.rootFolderPath, filePath).replace(/\\/g, '/');

    // 如果路径存在于 info.json 中，则删除
    if (infoData[relativePath]) {
      delete infoData[relativePath];

      // 保存更新后的 info.json
      fs.writeFileSync(infoPath, JSON.stringify(infoData, null, 2));
      console.log('info.json 已更新（删除）');
    }
  } catch (error) {
    console.error('更新 info.json 失败（删除）:', error);
  }
}
// 确保prompt函数正确实现
function prompt(text, defaultValue = '') {
  return new Promise((resolve) => {
    document.getElementById('表格').style.display = 'block';
    document.getElementById('prompt-text').value = defaultValue;
    document.getElementById('prompt-old').textContent = "原名: " + defaultValue;

    let 保存 = document.getElementById('提交');
    保存.onclick = function () {
      document.getElementById('表格').style.display = 'none';
      const newname = document.getElementById('prompt-text').value;
      resolve(newname);
    };

    let 取消 = document.getElementById('取消');
    取消.onclick = function () {
      document.getElementById('表格').style.display = 'none';
      resolve(null); // 用户取消时返回null
    };
  });
}

async function addDragEvents(thumbnail) {
  // 拖动开始事件
  thumbnail.addEventListener('dragstart', (e) => {
    const currentFilePath = thumbnail.dataset.path;

    // 检查是否有多个选中的文件
    //const selectedFiles = window.selectedFiles || new Set();
    let filesToDrag = [];

    if (selectedFiles.size > 1 && selectedFiles.has(currentFilePath)) {
      // 如果有多个选中文件且当前文件在选中列表中，拖拽所有选中的文件
      filesToDrag = Array.from(selectedFiles);
      console.log('DragStart - 开始拖动多个文件:', filesToDrag);
    } else {
      // 否则只拖拽当前文件
      filesToDrag = [currentFilePath];
      console.log('DragStart - 开始拖动单个文件:', currentFilePath);
    }

    // 设置拖拽效果为移动（仅限内部移动）
    e.dataTransfer.effectAllowed = 'move';

    // 只设置内部拖拽需要的JSON格式数据
    e.dataTransfer.setData('application/json', JSON.stringify(filesToDrag));

    console.log('设置内部拖拽数据:', filesToDrag);

    // 视觉反馈
    if (selectedFiles.size > 1) {
      // 为所有选中的文件添加拖拽效果
      document.querySelectorAll('.thumbnail-container.selected').forEach(container => {
        container.style.opacity = '0.5';
      });
    } else {
      thumbnail.style.opacity = '0.5';
    }

    document.body.style.cursor = 'grabbing';

    // 存储拖拽的文件列表供dragend事件使用
    window.currentDragFiles = filesToDrag;
  });

  // 拖动结束事件
  thumbnail.addEventListener('dragend', (e) => {
    console.log('DragEnd - 拖动结束');

    // 恢复所有缩略图的透明度
    document.querySelectorAll('.thumbnail-container').forEach(container => {
      container.style.opacity = '1';
    });
    document.body.style.cursor = '';

    // 清理临时数据
    window.currentDragFiles = null;
  });
}

module.exports = { addDragEvents };

// 添加全局拖拽到外部软件的功能
document.addEventListener('dragstart', (e) => {
  // 检查是否是文件元素
  if (e.target.closest('.thumbnail-container')) {
    const container = e.target.closest('.thumbnail-container');
    const filePath = container.dataset.file;

    if (filePath) {
      console.log('开始拖拽文件到外部软件:', filePath);

      // 通过IPC发送消息到主进程，启动系统级拖拽
      const { ipcRenderer } = require('electron');
      ipcRenderer.send('start-external-drag', { filePath });

      // 设置拖拽效果
      e.dataTransfer.effectAllowed = 'copy';
      e.dataTransfer.setData('text/plain', filePath);
    }
  }
});


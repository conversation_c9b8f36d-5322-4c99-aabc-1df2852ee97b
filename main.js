const { app, BrowserWindow, ipcMain, dialog } = require('electron')
const path = require('path')
const fs = require('fs') // 添加fs模块引入
const ApiServer = require('./src/apiServer')

let mainWindow
let apiServer

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    icon: path.join(__dirname, 'ass/app.png'), // 设置应用图标
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  })

  mainWindow.loadFile('index.html')
  
  // 开发工具
//  mainWindow.webContents.openDevTools()
}

app.whenReady().then(async () => {
  // 设置macOS Dock图标
  if (process.platform === 'darwin') {
    try {
      const iconPath = path.join(__dirname, 'ass/app.png')
      if (fs.existsSync(iconPath)) {
        app.dock.setIcon(iconPath)
      }
    } catch (error) {
      console.warn('Failed to set dock icon:', error.message)
    }
  }
  createWindow()

  // 启动API服务器
  try {
    apiServer = new ApiServer()
    const port = await apiServer.start()
    console.log(`API服务器已启动在端口: ${port}`)

    // 将端口信息发送给渲染进程
    mainWindow.webContents.once('did-finish-load', () => {
      mainWindow.webContents.send('api-server-started', { port })
    })
  } catch (error) {
    console.error('启动API服务器失败:', error)
  }

  // 处理打开文件夹对话框
  ipcMain.handle('open-folder-dialog', async () => {
    const result = await dialog.showOpenDialog({
      properties: ['openDirectory']
    })

    // 如果选择了文件夹，设置为全局根路径
    if (!result.canceled && result.filePaths.length > 0) {
      global.rootFolderPath = result.filePaths[0]
      console.log('设置根文件夹路径:', global.rootFolderPath)
    }

    return result
  })

  // 处理设置根文件夹路径的IPC调用
  ipcMain.handle('set-root-folder-path', async (event, rootPath) => {
    global.rootFolderPath = rootPath
    console.log('通过IPC设置根文件夹路径:', global.rootFolderPath)
    return { success: true }
  })
})

app.on('window-all-closed', () => {
  // 停止API服务器
  if (apiServer) {
    apiServer.stop()
  }

  if (process.platform !== 'darwin') app.quit()
})

app.on('before-quit', () => {
  // 应用退出前停止API服务器
  if (apiServer) {
    apiServer.stop()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})

// 添加打开外部文件的IPC处理
ipcMain.handle('open-file-external', async (event, filePath) => {
  const { shell } = require('electron')
  console.log('open-file-external 调用:', filePath);
  try {
    const result = await shell.openPath(filePath)
    console.log('openPath 结果:', result);
    if (result === '') {
      console.log('文件成功打开');
      return { success: true }
    } else {
      console.error('打开文件返回非空结果:', result);
      return { success: false, error: `系统返回: ${result}` }
    }
  } catch (error) {
    console.error('打开文件失败:', error)
    return { success: false, error: error.message }
  }
})

// 添加第二种外部打开文件方法
ipcMain.handle('open-file-external2', async (event, filePath) => {
  const { shell } = require('electron')
  try {
    await shell.openExternal(`file://${filePath}`)
    return { success: true }
  } catch (error) {
    console.error('方法2打开文件失败:', error)
    return { success: false, error: error.message }
  }
});

// 添加确认对话框IPC处理
ipcMain.handle('show-confirm-dialog', async (event, options) => {
  return dialog.showMessageBox({
    type: 'question',
    buttons: options.buttons || ['确定', '取消'],
    message: options.message,
    defaultId: 1
  });
});

// 添加移动文件的IPC处理
ipcMain.handle('move-file', async (event, { oldPath, newPath }) => {
  const fs = require('fs')
  const path = require('path')

  try {
    // 确保目标目录存在
    const dir = path.dirname(newPath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    // 移动文件
    fs.renameSync(oldPath, newPath)
    return { success: true }
  } catch (error) {
    console.error('移动文件失败:', error)
    return { success: false, error: error.message }
  }
})

// 添加复制文件的IPC处理
ipcMain.handle('copy-file', async (event, { sourcePath, targetPath }) => {
  const fs = require('fs')
  const path = require('path')

  try {
    // 确保目标目录存在
    const dir = path.dirname(targetPath)
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }

    // 复制文件
    fs.copyFileSync(sourcePath, targetPath)
    return { success: true }
  } catch (error) {
    console.error('复制文件失败:', error)
    return { success: false, error: error.message }
  }
})

// 添加复制文件到剪贴板的IPC处理
ipcMain.handle('copy-files-to-clipboard', async (event, filePaths) => {
  const { clipboard } = require('electron')

  try {
    // 在macOS上，我们需要使用特定的格式来复制文件
    if (process.platform === 'darwin') {
      // macOS使用NSFilenamesPboardType
      const fileUrls = filePaths.map(fp => `file://${fp}`)
      clipboard.write({
        text: filePaths.join('\n'),
        bookmark: fileUrls[0]
      })
    } else if (process.platform === 'win32') {
      // Windows使用CF_HDROP格式
      clipboard.writeBuffer('FileNameW', Buffer.from(filePaths.join('\0') + '\0\0', 'utf16le'))
    } else {
      // Linux使用text/uri-list
      const fileUrls = filePaths.map(fp => `file://${fp}`).join('\n')
      clipboard.writeText(fileUrls)
    }

    return { success: true }
  } catch (error) {
    console.error('复制文件到剪贴板失败:', error)
    return { success: false, error: error.message }
  }
})

// 处理外部拖拽请求
ipcMain.on('start-external-drag', (event, { filePath }) => {
  console.log('收到外部拖拽请求:', filePath)

  try {
    // 使用 webContents.startDrag 启动系统级拖拽
    event.sender.startDrag({
      file: filePath,
      icon: path.join(__dirname, 'ass/app.png') // 使用应用图标作为拖拽图标
    })
    console.log('系统级拖拽已启动')
  } catch (error) {
    console.error('启动系统级拖拽失败:', error)
  }
})
